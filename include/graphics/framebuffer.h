#pragma once

#include <glad/glad.h>
#include "texture.h"
#include <memory>

class Framebuffer {
public:
    Framebuffer();
    ~Framebuffer();
    
    // フレームバッファの作成
    bool create(int width, int height);
    
    // カラーテクスチャの添付
    bool attachColorTexture(std::shared_ptr<Texture> texture, int attachment = 0);
    
    // フレームバッファのバインド
    void bind() const;
    void unbind() const;
    
    // ピクセルデータの読み出し
    void readPixels(int x, int y, int width, int height, 
                   GLenum format, GLenum type, void* data) const;
    
    // フレームバッファIDの取得
    GLuint getID() const { return framebufferID; }
    
    // サイズの取得
    int getWidth() const { return width; }
    int getHeight() const { return height; }
    
    // 有効性の確認
    bool isValid() const;

private:
    GLuint framebufferID;
    int width, height;
    std::shared_ptr<Texture> colorTexture;
};
