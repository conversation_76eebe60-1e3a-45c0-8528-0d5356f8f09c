#pragma once

#include <glad/glad.h>
#include <vector>

class Texture {
public:
    Texture();
    ~Texture();
    
    // テクスチャの作成
    bool create(int width, int height, GLenum internalFormat = GL_RGBA32F, 
                GLenum format = GL_RGBA, GLenum type = GL_FLOAT);
    
    // データテクスチャの作成（音響パラメータ用）
    bool createDataTexture(int width, int height = 1, GLenum internalFormat = GL_RG32F,
                          GLenum format = GL_RG, GLenum type = GL_FLOAT);
    
    // テクスチャデータの更新
    void updateData(const void* data, int width, int height, 
                   GLenum format = GL_RGBA, GLenum type = GL_FLOAT);
    
    // テクスチャのバインド
    void bind(int unit = 0) const;
    void unbind() const;
    
    // テクスチャIDの取得
    GLuint getID() const { return textureID; }
    
    // サイズの取得
    int getWidth() const { return width; }
    int getHeight() const { return height; }
    
    // 有効性の確認
    bool isValid() const { return textureID != 0; }

private:
    GLuint textureID;
    int width, height;
    GLenum internalFormat, format, type;
};
