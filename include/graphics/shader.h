#pragma once

#include <glad/glad.h>
#include <string>
#include <unordered_map>
#include <glm/glm.hpp>

class Shader {
public:
    Shader();
    ~Shader();
    
    // シェーダーの読み込みとコンパイル
    bool loadFromFile(const std::string& vertexPath, const std::string& fragmentPath);
    bool loadFromString(const std::string& vertexSource, const std::string& fragmentSource);
    
    // シェーダーの使用
    void use() const;
    
    // uniform変数の設定
    void setFloat(const std::string& name, float value);
    void setInt(const std::string& name, int value);
    void setVec2(const std::string& name, const glm::vec2& value);
    void setVec3(const std::string& name, const glm::vec3& value);
    void setVec4(const std::string& name, const glm::vec4& value);
    void setMat4(const std::string& name, const glm::mat4& value);
    
    // プログラムIDの取得
    GLuint getProgram() const { return program; }
    
    // 有効性の確認
    bool isValid() const { return program != 0; }

private:
    GLuint program;
    mutable std::unordered_map<std::string, GLint> uniformCache;
    
    // ヘルパー関数
    GLuint compileShader(const std::string& source, GLenum type);
    GLint getUniformLocation(const std::string& name) const;
    std::string readFile(const std::string& path);
};
