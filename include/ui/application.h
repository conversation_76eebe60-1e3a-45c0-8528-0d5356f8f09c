#pragma once

#include <memory>
#include <map>
#include <vector>
#include <string>
#include <mutex>

struct GLFWwindow;

// 前方宣言
class AudioSystem;
class MidiSystem;
class Shader;
class Texture;
class Framebuffer;
class QuadRenderer;
struct MidiMessage;

class Application {
public:
    Application();
    ~Application();
    
    // アプリケーションの初期化
    bool initialize();
    
    // メインループの実行
    void run();
    
    // 終了処理
    void shutdown();

private:
    // ウィンドウとコンテキスト
    GLFWwindow* window;
    int windowWidth, windowHeight;
    
    // システム
    std::unique_ptr<AudioSystem> audioSystem;
    std::unique_ptr<MidiSystem> midiSystem;
    
    // グラフィックス
    std::unique_ptr<Shader> audioShader;
    std::unique_ptr<Shader> debugShader;
    std::shared_ptr<Texture> soundDataTexture;
    std::shared_ptr<Texture> waveTexture;
    std::unique_ptr<Framebuffer> audioFramebuffer;
    std::unique_ptr<QuadRenderer> quadRenderer;
    
    // 状態
    bool isAudioPlaying;
    std::map<int, double> activeNotes; // MIDIノート番号 -> 周波数
    std::vector<std::string> midiPorts;
    std::string selectedMidiPort;
    
    // オーディオ設定
    static const int AUDIO_BUFFER_SIZE = 512;
    static const int SAMPLE_RATE = 44100;
    
    // 時間管理
    double totalSamplesProcessed;
    
    // 波形表示用データ
    std::vector<float> waveformData;

    // GPU音響合成用のバッファ
    std::vector<float> gpuAudioBuffer;
    std::mutex audioBufferMutex;
    
    // 初期化メソッド
    bool initializeWindow();
    bool initializeOpenGL();
    bool initializeImGui();
    bool initializeAudio();
    bool initializeMidi();
    bool initializeGraphics();
    
    // イベントハンドラ
    void onMidiMessage(const MidiMessage& message);
    void onAudioCallback(float* buffer, int frames);
    
    // 描画メソッド
    void render();
    void renderUI();
    void renderWaveform();
    
    // ヘルパーメソッド
    void updateSoundDataTexture();
    void toggleAudio();
    void connectMidiPort(const std::string& portName);
    void generateGPUAudio(); // メインスレッドでGPU音響合成
    
    // GLFWコールバック
    static void framebufferSizeCallback(GLFWwindow* window, int width, int height);
};
