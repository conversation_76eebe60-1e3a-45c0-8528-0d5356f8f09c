#pragma once

#include <alsa/asoundlib.h>
#include <vector>
#include <functional>
#include <thread>
#include <atomic>
#include <mutex>

class AudioSystem {
public:
    using AudioCallback = std::function<void(float* buffer, int frames)>;
    
    AudioSystem();
    ~AudioSystem();
    
    // オーディオシステムの初期化
    bool initialize(int sampleRate = 44100, int bufferSize = 512);
    
    // オーディオの開始/停止
    bool start();
    void stop();
    
    // コールバック関数の設定
    void setAudioCallback(AudioCallback callback);
    
    // 設定の取得
    int getSampleRate() const { return sampleRate; }
    int getBufferSize() const { return bufferSize; }
    
    // 状態の確認
    bool isRunning() const { return running.load(); }
    bool isInitialized() const { return pcmHandle != nullptr; }

private:
    snd_pcm_t* pcmHandle;
    int sampleRate;
    int bufferSize;
    
    std::thread audioThread;
    std::atomic<bool> running;
    std::atomic<bool> shouldStop;
    
    AudioCallback audioCallback;
    std::mutex callbackMutex;
    
    std::vector<float> audioBuffer;
    
    // 内部メソッド
    void audioThreadFunction();
    bool setupAlsa();
    void cleanup();
};
