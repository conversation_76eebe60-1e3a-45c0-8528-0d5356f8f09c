#pragma once

#include <alsa/asoundlib.h>
#include <functional>
#include <thread>
#include <atomic>
#include <vector>
#include <string>
#include <map>

struct MidiMessage {
    unsigned char status;
    unsigned char data1;
    unsigned char data2;
    double timestamp;
};

class MidiSystem {
public:
    using MidiCallback = std::function<void(const MidiMessage& message)>;
    
    MidiSystem();
    ~MidiSystem();
    
    // MIDI システムの初期化
    bool initialize();
    
    // MIDI入力の開始/停止
    bool start();
    void stop();
    
    // 利用可能なMIDI入力ポートの取得
    std::vector<std::string> getInputPorts() const;
    
    // MIDI入力ポートの接続
    bool connectToPort(const std::string& portName);
    
    // コールバック関数の設定
    void setMidiCallback(MidiCallback callback);
    
    // 状態の確認
    bool isRunning() const { return running.load(); }
    bool isInitialized() const { return sequencer != nullptr; }
    
    // MIDIノート番号を周波数に変換
    static double midiNoteToFrequency(int note);

private:
    snd_seq_t* sequencer;
    int clientId;
    int portId;
    
    std::thread midiThread;
    std::atomic<bool> running;
    std::atomic<bool> shouldStop;
    
    MidiCallback midiCallback;
    
    std::map<std::string, snd_seq_addr_t> availablePorts;
    
    // 内部メソッド
    void midiThreadFunction();
    bool setupSequencer();
    void scanInputPorts();
    void processMidiEvent(const snd_seq_event_t* event);
    void cleanup();
};
