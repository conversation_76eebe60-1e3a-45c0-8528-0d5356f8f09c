// main.js

document.addEventListener('DOMContentLoaded', async () => {
    // --- グローバル変数と定数 ---
    const DEBUG_CANVAS = document.getElementById('debugCanvas');
    const TOGGLE_BUTTON = document.getElementById('toggleButton');
    const MIDI_STATUS = document.getElementById('midiStatus');

    // オーディオ設定
    const AUDIO_BUFFER_SIZE = 512; // 一度に生成するサンプル数

    // --- シェーダーソース定義 ---
    const GPGPU_VERTEX_SHADER = `#version 300 es
        in vec2 a_position;
        void main() { gl_Position = vec4(a_position, 0.0, 1.0); }`;
    
    const DEBUG_VERTEX_SHADER = `#version 300 es
        in vec2 a_position;
        out vec2 v_texcoord;
        void main() { 
            gl_Position = vec4(a_position, 0.0, 1.0);
            v_texcoord = a_position * 0.5 + 0.5;
        }`;
    
    const DEBUG_FRAGMENT_SHADER = `#version 300 es
        precision highp float;
        uniform sampler2D u_texture;
        in vec2 v_texcoord;
        out vec4 outColor;
        void main() {
            // テクスチャが存在しない場合(オーディオ開始前)は黒くする
            if (textureSize(u_texture, 0).x < 2) {
                outColor = vec4(0.0, 0.0, 0.0, 1.0);
                return;
            }
            float wave = texture(u_texture, v_texcoord).r;
            // 波形を見やすくするためにY軸を拡大
            float y = (v_texcoord.y - 0.5) * 2.0; // -1 to 1
            float wave_y = (wave - 0.5) * 2.0;    // -1 to 1
            float line = 1.0 - smoothstep(0.0, 0.05 / (256.0 / 2.0), abs(y - wave_y));
            outColor = vec4(vec3(line), 1.0);
        }`;


    // --- 状態変数 ---
    let audioContext;
    let scriptNode;
    let gl; // WebGLコンテキスト
    let gpgpuProgram; // 音生成用シェーダープログラム
    let debugProgram; // デバッグ表示用シェーダープログラム
    let fbo; // フレームバッファオブジェクト
    let waveTexture; // 音の波形を書き込むテクスチャ
    let soundDataTexture; // 音のパラメータを送るデータテクスチャ
    let quadBuffer; // 画面全体を覆う板ポリゴン
    let gainNode; // ScriptProcessorNodeを確実にアクティブにするためのGainNode

    let totalSamplesProcessed = 0;
    let isPlaying = false;
    let activeNotes = new Map(); // MIDIノート番号 -> 周波数
    let midiAccess = null;
    let midiInput = null;
    let textureUpdateTimeout = null; // テクスチャ更新用のタイムアウトID


    // --- メイン処理 ---
    
    // WebGLを初期化
    if (!setupWebGL()) return;
    
    // デバッグ表示ループを開始
    renderDebugView();

    // スタート/ストップボタン
    TOGGLE_BUTTON.addEventListener('click', toggleAudio);


    // --- 関数定義 ---

    // WebGLの初期化
    function setupWebGL() {
        gl = DEBUG_CANVAS.getContext('webgl2');
        if (!gl) {
            alert('WebGL2をサポートしているブラウザが必要です。');
            return false;
        }

        if (!gl.getExtension('EXT_color_buffer_float')) {
            alert('浮動小数点数テクスチャがサポートされていません。');
            return false;
        }
        
        // 初期化時に必要なオブジェクトを生成
        quadBuffer = createQuadBuffer();
        soundDataTexture = createDataTexture(1); // 最初は1x1
        waveTexture = createDataTexture(AUDIO_BUFFER_SIZE); // デバッグ用に最初から作成
        
        return true;
    }

    // オーディオの開始/停止
    async function toggleAudio() {
        if (isPlaying) {
            // 停止
            scriptNode.disconnect();
            await audioContext.close();
            isPlaying = false;
            TOGGLE_BUTTON.textContent = 'Start Audio';
        } else {
            // 開始
            audioContext = new (window.AudioContext || window.webkitAudioContext)();
            await audioContext.resume();
            scriptNode = audioContext.createScriptProcessor(AUDIO_BUFFER_SIZE, 0, 1);
            gainNode = audioContext.createGain(); // GainNodeを作成
            scriptNode.connect(gainNode); // scriptNodeをgainNodeに接続
            gainNode.connect(audioContext.destination); // gainNodeをdestinationに接続
            
            // シェーダーとプログラムを初期化 (初回のみ)
            if (!gpgpuProgram) {
                // ★★★★★ 修正点 ★★★★★
                // .trim()を追加してシェーダーコード先頭の空白やBOMを削除
                const fragShaderSource = await fetch('shader.frag').then(res => res.text()).then(text => text.trim());
                gpgpuProgram = createProgram(createShader(gl, gl.VERTEX_SHADER, GPGPU_VERTEX_SHADER), createShader(gl, gl.FRAGMENT_SHADER, fragShaderSource));
                fbo = createFBO(waveTexture);
            }

            scriptNode.onaudioprocess = handleAudioProcess;
        }
    }
    
    // オーディオが再生バッファを要求するたびに呼ばれる
    function handleAudioProcess(e) {
        // 1. GPGPUで波形を生成
        gl.useProgram(gpgpuProgram);

        gl.bindFramebuffer(gl.FRAMEBUFFER, fbo);
        gl.viewport(0, 0, AUDIO_BUFFER_SIZE, 1);

        const timeOffset = totalSamplesProcessed / audioContext.sampleRate;
        gl.uniform1f(gl.getUniformLocation(gpgpuProgram, 'u_time_offset'), timeOffset);
        gl.uniform1f(gl.getUniformLocation(gpgpuProgram, 'u_sample_rate'), audioContext.sampleRate);
        
        gl.activeTexture(gl.TEXTURE0);
        gl.bindTexture(gl.TEXTURE_2D, soundDataTexture);
        gl.uniform1i(gl.getUniformLocation(gpgpuProgram, 'u_sound_data'), 0);
        
        gl.bindBuffer(gl.ARRAY_BUFFER, quadBuffer);
        const posLoc = gl.getAttribLocation(gpgpuProgram, 'a_position');
        gl.enableVertexAttribArray(posLoc);
        gl.vertexAttribPointer(posLoc, 2, gl.FLOAT, false, 0, 0);
        gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);

        // 2. GPUからピクセルデータ(波形)を読み出す
        const waveData = new Float32Array(AUDIO_BUFFER_SIZE * 4);
        gl.readPixels(0, 0, AUDIO_BUFFER_SIZE, 1, gl.RGBA, gl.FLOAT, waveData);
        
        // 3. Web Audioのバッファにコピー
        const outputBuffer = e.outputBuffer.getChannelData(0);
        for (let i = 0; i < AUDIO_BUFFER_SIZE; i++) {
            outputBuffer[i] = (waveData[i * 4] - 0.5) * 2.0;
        }

        totalSamplesProcessed += AUDIO_BUFFER_SIZE;
        gl.bindFramebuffer(gl.FRAMEBUFFER, null);
    }

    // データテクスチャを現在の音の状態に合わせて更新
    function updateSoundDataTexture() {
        if (!gl || !soundDataTexture) {
            console.warn('updateSoundDataTexture: WebGLまたはsoundDataTextureが未初期化です。');
            return;
        }
        const frequencies = Array.from(activeNotes.values());
        const numNotes = Math.max(1, frequencies.length);

        const soundData = new Float32Array(numNotes * 2);
        for (let i = 0; i < frequencies.length; i++) {
            soundData[i * 2 + 0] = frequencies[i];
            soundData[i * 2 + 1] = 1.0; // 振幅は常に1.0
        }
        gl.bindTexture(gl.TEXTURE_2D, soundDataTexture);
        gl.texImage2D(gl.TEXTURE_2D, 0, gl.RG32F, numNotes, 1, 0, gl.RG, gl.FLOAT, soundData);
    }
    
    // デバッグ表示用の描画ループ
    function renderDebugView() {
        if (!debugProgram) {
            debugProgram = createProgram(createShader(gl, gl.VERTEX_SHADER, DEBUG_VERTEX_SHADER), createShader(gl, gl.FRAGMENT_SHADER, DEBUG_FRAGMENT_SHADER));
        }

        gl.bindFramebuffer(gl.FRAMEBUFFER, null); // 画面に描画
        gl.useProgram(debugProgram);
        gl.viewport(0, 0, DEBUG_CANVAS.width, DEBUG_CANVAS.height);

        gl.activeTexture(gl.TEXTURE0);
        gl.bindTexture(gl.TEXTURE_2D, waveTexture);
        gl.uniform1i(gl.getUniformLocation(debugProgram, 'u_texture'), 0);

        gl.bindBuffer(gl.ARRAY_BUFFER, quadBuffer);
        const posLoc = gl.getAttribLocation(debugProgram, 'a_position');
        gl.enableVertexAttribArray(posLoc);
        gl.vertexAttribPointer(posLoc, 2, gl.FLOAT, false, 0, 0);
        gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);

        requestAnimationFrame(renderDebugView);
    }

    // --- WebGL ヘルパー関数 ---

    function createShader(gl, type, source) {
        const shader = gl.createShader(type);
        gl.shaderSource(shader, source);
        gl.compileShader(shader);
        if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
            console.error('Shader compile error:', gl.getShaderInfoLog(shader));
            gl.deleteShader(shader);
            return null;
        }
        return shader;
    }

    function createProgram(vertexShader, fragmentShader) {
        const program = gl.createProgram();
        gl.attachShader(program, vertexShader);
        gl.attachShader(program, fragmentShader);
        gl.linkProgram(program);
        if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
            console.error('Program link error:', gl.getProgramInfoLog(program));
            gl.deleteProgram(program);
            return null;
        }
        return program;
    }
    
    function createDataTexture(width) {
        const texture = gl.createTexture();
        gl.bindTexture(gl.TEXTURE_2D, texture);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.NEAREST);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.NEAREST);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
        gl.texImage2D(gl.TEXTURE_2D, 0, gl.RG32F, width, 1, 0, gl.RG, gl.FLOAT, null);
        return texture;
    }
    
    function createFBO(texture) {
        const fbo = gl.createFramebuffer();
        gl.bindFramebuffer(gl.FRAMEBUFFER, fbo);
        gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture, 0);
        gl.bindFramebuffer(gl.FRAMEBUFFER, null);
        return fbo;
    }

    function createQuadBuffer() {
        const buffer = gl.createBuffer();
        gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
        gl.bufferData(gl.ARRAY_BUFFER, new Float32Array([-1, -1, 1, -1, -1, 1, 1, 1]), gl.STATIC_DRAW);
        return buffer;
    }

    // MIDI初期化
    async function initMidi() {
        if (!navigator.requestMIDIAccess) {
            MIDI_STATUS.textContent = 'MIDI: Web MIDI APIがサポートされていません。';
            return;
        }

        try {
            midiAccess = await navigator.requestMIDIAccess();
            MIDI_STATUS.textContent = 'MIDI: 接続済み';
            midiAccess.onstatechange = onMidiStateChange;
            updateMidiInputs(midiAccess);
        } catch (e) {
            MIDI_STATUS.textContent = `MIDI: エラー - ${e.message}`;
        }
    }

    function onMidiStateChange(event) {
        if (event.port.type === 'input') {
            updateMidiInputs(midiAccess);
        }
    }

    function updateMidiInputs(midiAccess) {
        midiInput = null;
        for (let input of midiAccess.inputs.values()) {
            // 最初の利用可能なMIDI入力ポートを使用
            midiInput = input;
            break;
        }

        if (midiInput) {
            MIDI_STATUS.textContent = `MIDI: ${midiInput.name} に接続中`;
            midiInput.onmidimessage = onMidiMessage;
        } else {
            MIDI_STATUS.textContent = 'MIDI: 入力デバイスが見つかりません';
        }
    }

    function onMidiMessage(event) {
        const [command, note, velocity] = event.data;
        // Note On (0x9n) または Note Off (0x8n) メッセージを処理
        if ((command & 0xF0) === 0x90 && velocity > 0) { // Note On (任意のチャンネル)
            const frequency = midiNoteToFrequency(note);
            activeNotes.set(note, frequency);
        } else if ((command & 0xF0) === 0x80 || ((command & 0xF0) === 0x90 && velocity === 0)) { // Note Off (任意のチャンネル)
            activeNotes.delete(note);
        }
        // updateSoundDataTextureをデバウンス
        if (textureUpdateTimeout) {
            clearTimeout(textureUpdateTimeout);
        }
        textureUpdateTimeout = setTimeout(() => {
            updateSoundDataTexture();
            textureUpdateTimeout = null;
        }, 10); // 10msのデバウンス時間
    }

    // MIDIノート番号を周波数に変換
    function midiNoteToFrequency(note) {
        return 440 * Math.pow(2, (note - 69) / 12);
    }

    initMidi();
});