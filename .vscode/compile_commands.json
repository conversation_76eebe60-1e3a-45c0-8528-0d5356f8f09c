[{"directory": "/home/<USER>/Downloads/GLAudTest", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-g", "-std=c++17", "-Isrc", "-Iin<PERSON><PERSON>", "-DNDEBUG", "-o", "build/.objs/glaudtest/linux/x86_64/src/main.cpp.o", "src/main.cpp"], "file": "src/main.cpp"}, {"directory": "/home/<USER>/Downloads/GLAudTest", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-g", "-std=c++17", "-Isrc", "-Iin<PERSON><PERSON>", "-DNDEBUG", "-o", "build/.objs/glaudtest/linux/x86_64/src/audio/audio_system.cpp.o", "src/audio/audio_system.cpp"], "file": "src/audio/audio_system.cpp"}, {"directory": "/home/<USER>/Downloads/GLAudTest", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-g", "-std=c++17", "-Isrc", "-Iin<PERSON><PERSON>", "-DNDEBUG", "-o", "build/.objs/glaudtest/linux/x86_64/src/graphics/shader.cpp.o", "src/graphics/shader.cpp"], "file": "src/graphics/shader.cpp"}, {"directory": "/home/<USER>/Downloads/GLAudTest", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-g", "-std=c++17", "-Isrc", "-Iin<PERSON><PERSON>", "-DNDEBUG", "-o", "build/.objs/glaudtest/linux/x86_64/src/graphics/texture.cpp.o", "src/graphics/texture.cpp"], "file": "src/graphics/texture.cpp"}, {"directory": "/home/<USER>/Downloads/GLAudTest", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-g", "-std=c++17", "-Isrc", "-Iin<PERSON><PERSON>", "-DNDEBUG", "-o", "build/.objs/glaudtest/linux/x86_64/src/graphics/framebuffer.cpp.o", "src/graphics/framebuffer.cpp"], "file": "src/graphics/framebuffer.cpp"}, {"directory": "/home/<USER>/Downloads/GLAudTest", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-g", "-std=c++17", "-Isrc", "-Iin<PERSON><PERSON>", "-DNDEBUG", "-o", "build/.objs/glaudtest/linux/x86_64/src/graphics/quad_renderer.cpp.o", "src/graphics/quad_renderer.cpp"], "file": "src/graphics/quad_renderer.cpp"}, {"directory": "/home/<USER>/Downloads/GLAudTest", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-g", "-std=c++17", "-Isrc", "-Iin<PERSON><PERSON>", "-DNDEBUG", "-o", "build/.objs/glaudtest/linux/x86_64/src/midi/midi_system.cpp.o", "src/midi/midi_system.cpp"], "file": "src/midi/midi_system.cpp"}, {"directory": "/home/<USER>/Downloads/GLAudTest", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-g", "-std=c++17", "-Isrc", "-Iin<PERSON><PERSON>", "-DNDEBUG", "-o", "build/.objs/glaudtest/linux/x86_64/src/ui/application.cpp.o", "src/ui/application.cpp"], "file": "src/ui/application.cpp"}]