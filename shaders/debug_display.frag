#version 330 core

precision highp float;
uniform sampler2D u_texture;
in vec2 v_texcoord;
out vec4 outColor;

void main() {
    // テクスチャが存在しない場合(オーディオ開始前)は黒くする
    if (textureSize(u_texture, 0).x < 2) {
        outColor = vec4(0.0, 0.0, 0.0, 1.0);
        return;
    }
    float wave = texture(u_texture, v_texcoord).r;
    // 波形を見やすくするためにY軸を拡大
    float y = (v_texcoord.y - 0.5) * 2.0; // -1 to 1
    float wave_y = (wave - 0.5) * 2.0;    // -1 to 1
    float line = 1.0 - smoothstep(0.0, 0.05 / (256.0 / 2.0), abs(y - wave_y));
    outColor = vec4(vec3(line), 1.0);
}
