#version 330 core

precision highp float;

// JavaScriptから受け取る uniform 変数
uniform sampler2D u_sound_data; // 音のパラメータ(周波数, 振幅)が入ったテクスチャ
uniform float u_time_offset;    // 全体の経過時間オフセット(秒)
uniform float u_sample_rate;    // サンプリングレート (例: 44100.0)

// 出力変数
out vec4 outColor;

const float PI = 3.14159265359;

void main() {
    // データテクスチャのサイズを取得 (横幅 = 鳴らす音の数)
    ivec2 data_tex_size = textureSize(u_sound_data, 0);
    int num_sounds = data_tex_size.x;

    // 現在のピクセル座標から、このサンプルが何番目かを計算
    // gl_FragCoord.x は 0.5 から始まるので floor を使う
    float sample_index = floor(gl_FragCoord.x);
    
    // このサンプルに対応する時間を計算
    float current_time = u_time_offset + (sample_index / u_sample_rate);
    
    float final_amplitude = 0.0;
    float total_amp_sum = 0.0;

    // データテクスチャから各音の情報を読み出して合成する
    for (int i = 0; i < num_sounds; i++) {
        // テクスチャの i 番目のピクセルからデータを取得
        vec4 data = texelFetch(u_sound_data, ivec2(i, 0), 0);
        
        float freq = data.r; // Rチャンネルに周波数
        float amp  = data.g; // Gチャンネルに振幅
        
        // 周波数が0より大きい場合のみ音を合成
        if (freq > 0.0 && amp > 0.0) {
            float phase = mod(freq * current_time, 1.0);
            final_amplitude += sin(2.0 * PI * phase) * amp;
            total_amp_sum += amp;
        }
    }
    
    // 振幅 (-1.0 ~ 1.0) を色 (0.0 ~ 1.0) にマッピングしてRチャンネルに書き込む
    // この値が glReadPixels で読み出される
    outColor = vec4(clamp(final_amplitude, -1.0, 1.0) * 0.5 + 0.5, 0.0, 0.0, 1.0);
}
