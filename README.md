# GLAudTest - Shader Audio Synthesis

WebGLシェーダーを使った音響合成デモのC++/ImGui移植版です。GPUシェーダーでリアルタイムにオーディオ波形を生成し、ALSAを通じて再生します。

## 特徴

- **GPU音響合成**: OpenGLフラグメントシェーダーでオーディオサンプルを直接生成
- **リアルタイム再生**: ALSAを使用したリアルタイムオーディオ出力
- **MIDI入力対応**: ALSA MIDIを使用してMIDIキーボードからの入力を処理
- **波形可視化**: 生成された波形をリアルタイムで表示
- **ImGuiインターフェース**: 直感的なユーザーインターフェース

## 必要な環境

- Linux (ALSA対応)
- OpenGL 3.3以上
- C++17対応コンパイラ
- xmake
- Nix (推奨)

## ビルドと実行

### Nixを使用する場合（推奨）

```bash
# 開発環境に入る
nix-shell

# ビルド
xmake

# 実行
xmake run
```

### 手動でビルドする場合

必要な依存関係をインストール:
```bash
# Ubuntu/Debian
sudo apt install libasound2-dev libgl1-mesa-dev libglfw3-dev

# Arch Linux
sudo pacman -S alsa-lib mesa glfw-x11

# Fedora
sudo dnf install alsa-lib-devel mesa-libGL-devel glfw-devel
```

ビルド:
```bash
xmake
xmake run
```

## 使用方法

1. **アプリケーションの起動**
   - `xmake run` でアプリケーションを起動

2. **MIDI設定**
   - MIDIデバイスを接続
   - UIでMIDIポートを選択
   - MIDIキーボードで演奏

3. **オーディオ再生**
   - "Start Audio" ボタンでオーディオ出力を開始
   - MIDIノートに応じてリアルタイムで音が生成される

4. **波形表示**
   - 右側のウィンドウでリアルタイム波形を確認

## MIDI設定

利用可能なMIDIデバイスを確認:
```bash
# MIDIポートの一覧
aconnect -l

# MIDIデバイスの一覧
amidi -l
```

## トラブルシューティング

### オーディオが出力されない
- ALSAの設定を確認: `alsamixer`
- オーディオデバイスの権限を確認
- PulseAudioとの競合を確認

### MIDIが認識されない
- MIDIデバイスの接続を確認
- ユーザーがaudioグループに所属しているか確認:
  ```bash
  sudo usermod -a -G audio $USER
  ```

### ビルドエラー
- 依存関係が正しくインストールされているか確認
- OpenGLドライバーが最新か確認

## 技術詳細

### アーキテクチャ
- **Graphics**: OpenGL 3.3、シェーダー管理、テクスチャ、フレームバッファ
- **Audio**: ALSA PCM API、リアルタイムオーディオストリーミング
- **MIDI**: ALSA Sequencer API、MIDIイベント処理
- **UI**: ImGui、GLFW

### シェーダー
- `audio_generation.frag`: 音響合成用フラグメントシェーダー
- `debug_display.frag`: 波形表示用シェーダー

### 音響合成
1. MIDIノートから周波数を計算
2. 周波数データをテクスチャに格納
3. シェーダーでサイン波を合成
4. GPUから波形データを読み出し
5. ALSAでリアルタイム再生

## ライセンス

このプロジェクトはMITライセンスの下で公開されています。
