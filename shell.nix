{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    # ビルドツール
    xmake
    gcc
    cmake
    pkg-config
    
    # OpenGL関連
    libGL
    libGLU
    glfw
    
    # オーディオ関連
    alsa-lib
    alsa-utils
    alsa-tools
    
    # その他の開発ツール
    gdb
    valgrind
    
    # X11関連（Linux GUI用）
    xorg.libX11
    xorg.libXcursor
    xorg.libXrandr
    xorg.libXinerama
    xorg.libXi
    
    # Wayland関連（オプション）
    wayland
    wayland-protocols
    libxkbcommon
  ];
  
  shellHook = ''
    echo "GLAudTest開発環境へようこそ！"
    echo "使用可能なコマンド:"
    echo "  xmake          - プロジェクトをビルド"
    echo "  xmake run      - アプリケーションを実行"
    echo "  xmake clean    - ビルドファイルをクリーンアップ"
    echo ""
    echo "ALSA MIDIデバイスの確認:"
    echo "  aconnect -l    - 利用可能なMIDIポートを表示"
    echo "  amidi -l       - MIDIデバイスを一覧表示"
    echo ""
    
    # 環境変数の設定
    export PKG_CONFIG_PATH="${pkgs.alsa-lib}/lib/pkgconfig:$PKG_CONFIG_PATH"
    export LD_LIBRARY_PATH="${pkgs.alsa-lib}/lib:${pkgs.libGL}/lib:$LD_LIBRARY_PATH"
    
    # ALSAの設定確認
    if [ ! -f ~/.asoundrc ]; then
      echo "注意: ~/.asoundrc が見つかりません。ALSA設定を確認してください。"
    fi
  '';
  
  # 環境変数
  NIX_CFLAGS_COMPILE = "-I${pkgs.alsa-lib}/include";
  NIX_LDFLAGS = "-L${pkgs.alsa-lib}/lib";
}
