<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>シェーダーで音を鳴らす</title>
    <style>
        body { font-family: sans-serif; background: #222; color: #eee; padding: 1em; }
        h1, h2 { font-weight: normal; border-bottom: 1px solid #555; padding-bottom: 0.5em; }
        #controls { margin-bottom: 1.5em; }
        #notes label { display: inline-block; margin-right: 1em; cursor: pointer; }
        #debugCanvas { border: 1px solid #555; background: #333; }
        button { font-size: 1.2em; padding: 0.5em 1em; cursor: pointer; }
        a { color: #6af; }
    </style>
</head>
<body>
    <h1>シェーダーで音を鳴らすデモ</h1>
    <p>GPUシェーダーで直接オーディオ波形を生成し、Web Audio APIで再生します。</p>

    <div id="controls">
        <button id="toggleButton">Start Audio</button>
        <p id="midiStatus">MIDI: 接続されていません</p>
    </div>

    <h2>デバッグ表示 (シェーダーが生成した波形テクスチャ)</h2>
    <canvas id="debugCanvas" width="512" height="256"></canvas>

    <!-- メインのスクリプト -->
    <script src="main.js"></script>
</body>
</html>
