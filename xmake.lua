-- xmake.lua
set_project("GLAudTest")
set_version("1.0.0")

-- C++17標準を使用
set_languages("c++17")

-- デバッグ情報を含める
set_symbols("debug")

-- パッケージの追加
add_requires("glfw", "glad", "imgui", "glm")
add_requires("alsa", {system = true})

-- メインターゲット
target("glaudtest")
    set_kind("binary")
    
    -- ソースファイル
    add_files("src/*.cpp")
    add_files("src/**/*.cpp")
    
    -- ヘッダーファイルのインクルードパス
    add_includedirs("src")
    add_includedirs("include")
    
    -- パッケージの使用
    add_packages("glfw", "glad", "imgui", "glm")
    add_packages("alsa")
    
    -- システムライブラリ
    add_links("pthread", "dl", "m")
    
    -- OpenGLライブラリ
    add_links("GL")
    
    -- コンパイラフラグ
    add_cxxflags("-Wall", "-Wextra")
    
    -- デバッグビルド用の設定
    if is_mode("debug") then
        add_defines("DEBUG")
        set_optimize("none")
    else
        set_optimize("fastest")
        add_defines("NDEBUG")
    end
    
    -- リソースファイルのコピー
    after_build(function (target)
        os.cp("shaders", path.join(target:targetdir(), "shaders"))
    end)

-- デバッグモードをデフォルトに設定
set_defaultmode("debug")
