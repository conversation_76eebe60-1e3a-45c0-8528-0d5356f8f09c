#include "ui/application.h"
#include <iostream>

int main() {
    std::cout << "GLAudTest - Shader Audio Synthesis" << std::endl;
    std::cout << "====================================" << std::endl;
    
    Application app;
    
    if (!app.initialize()) {
        std::cerr << "Failed to initialize application" << std::endl;
        return -1;
    }
    
    std::cout << "Application initialized successfully" << std::endl;
    std::cout << "Starting main loop..." << std::endl;
    
    app.run();
    
    std::cout << "Application shutting down..." << std::endl;
    app.shutdown();
    
    return 0;
}
