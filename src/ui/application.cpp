#include "ui/application.h"
#include "audio/audio_system.h"
#include "midi/midi_system.h"
#include "graphics/shader.h"
#include "graphics/texture.h"
#include "graphics/framebuffer.h"
#include "graphics/quad_renderer.h"

#include <glad/glad.h>
#include <GLFW/glfw3.h>
#include <imgui.h>
#include <imgui_impl_glfw.h>
#include <imgui_impl_opengl3.h>

#include <iostream>
#include <algorithm>
#include <cmath>

Application::Application()
    : window(nullptr), windowWidth(1200), windowHeight(800),
      isAudioPlaying(false), totalSamplesProcessed(0.0) {
    waveformData.resize(AUDIO_BUFFER_SIZE);
    gpuAudioBuffer.resize(AUDIO_BUFFER_SIZE);
}

Application::~Application() {
    shutdown();
}

bool Application::initialize() {
    if (!initializeWindow()) return false;
    if (!initializeOpenGL()) return false;
    if (!initializeImGui()) return false;
    if (!initializeGraphics()) return false;
    if (!initializeAudio()) return false;
    if (!initializeMidi()) return false;
    
    return true;
}

void Application::run() {
    while (!glfwWindowShouldClose(window)) {
        glfwPollEvents();

        // GPU音響合成を実行（メインスレッド）
        if (isAudioPlaying) {
            generateGPUAudio();
        }

        render();

        glfwSwapBuffers(window);
    }
}

void Application::shutdown() {
    if (audioSystem) {
        audioSystem->stop();
    }
    if (midiSystem) {
        midiSystem->stop();
    }
    
    // ImGuiの終了処理
    ImGui_ImplOpenGL3_Shutdown();
    ImGui_ImplGlfw_Shutdown();
    ImGui::DestroyContext();
    
    // GLFWの終了処理
    if (window) {
        glfwDestroyWindow(window);
    }
    glfwTerminate();
}

bool Application::initializeWindow() {
    if (!glfwInit()) {
        std::cerr << "Failed to initialize GLFW" << std::endl;
        return false;
    }
    
    glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
    glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 3);
    glfwWindowHint(GLFW_OPENGL_PROFILE, GLFW_OPENGL_CORE_PROFILE);
    
    window = glfwCreateWindow(windowWidth, windowHeight, "GLAudTest - Shader Audio Synthesis", nullptr, nullptr);
    if (!window) {
        std::cerr << "Failed to create GLFW window" << std::endl;
        glfwTerminate();
        return false;
    }
    
    glfwMakeContextCurrent(window);
    glfwSetWindowUserPointer(window, this);
    glfwSetFramebufferSizeCallback(window, framebufferSizeCallback);
    
    return true;
}

bool Application::initializeOpenGL() {
    if (!gladLoadGLLoader((GLADloadproc)glfwGetProcAddress)) {
        std::cerr << "Failed to initialize GLAD" << std::endl;
        return false;
    }
    
    glViewport(0, 0, windowWidth, windowHeight);
    
    return true;
}

bool Application::initializeGraphics() {
    // シェーダーの初期化
    audioShader = std::make_unique<Shader>();
    if (!audioShader->loadFromFile("shaders/quad.vert", "shaders/audio_generation.frag")) {
        std::cerr << "Failed to load audio shader" << std::endl;
        return false;
    }
    std::cout << "Audio shader loaded successfully" << std::endl;

    debugShader = std::make_unique<Shader>();
    if (!debugShader->loadFromFile("shaders/debug_display.vert", "shaders/debug_display.frag")) {
        std::cerr << "Failed to load debug shader" << std::endl;
        return false;
    }

    // テクスチャの初期化
    soundDataTexture = std::make_shared<Texture>();
    if (!soundDataTexture->createDataTexture(1, 1, GL_RG32F, GL_RG, GL_FLOAT)) {
        std::cerr << "Failed to create sound data texture" << std::endl;
        return false;
    }

    waveTexture = std::make_shared<Texture>();
    if (!waveTexture->create(AUDIO_BUFFER_SIZE, 1, GL_RGBA32F, GL_RGBA, GL_FLOAT)) {
        std::cerr << "Failed to create wave texture" << std::endl;
        return false;
    }

    // フレームバッファの初期化
    audioFramebuffer = std::make_unique<Framebuffer>();
    if (!audioFramebuffer->create(AUDIO_BUFFER_SIZE, 1)) {
        std::cerr << "Failed to create audio framebuffer" << std::endl;
        return false;
    }

    if (!audioFramebuffer->attachColorTexture(waveTexture)) {
        std::cerr << "Failed to attach texture to framebuffer" << std::endl;
        return false;
    }

    // クアッドレンダラーの初期化
    quadRenderer = std::make_unique<QuadRenderer>();
    if (!quadRenderer->initialize()) {
        std::cerr << "Failed to initialize quad renderer" << std::endl;
        return false;
    }

    return true;
}

bool Application::initializeAudio() {
    audioSystem = std::make_unique<AudioSystem>();

    if (!audioSystem->initialize(SAMPLE_RATE, AUDIO_BUFFER_SIZE)) {
        std::cerr << "Failed to initialize audio system" << std::endl;
        return false;
    }

    // オーディオコールバックを設定
    audioSystem->setAudioCallback([this](float* buffer, int frames) {
        onAudioCallback(buffer, frames);
    });

    return true;
}

bool Application::initializeMidi() {
    midiSystem = std::make_unique<MidiSystem>();

    if (!midiSystem->initialize()) {
        std::cerr << "Failed to initialize MIDI system" << std::endl;
        return false;
    }

    // MIDIコールバックを設定
    midiSystem->setMidiCallback([this](const MidiMessage& message) {
        onMidiMessage(message);
    });

    // 利用可能なMIDIポートを取得
    midiPorts = midiSystem->getInputPorts();

    // MIDIシステムを開始
    midiSystem->start();

    return true;
}

void Application::onMidiMessage(const MidiMessage& message) {
    // Note On (0x9n) または Note Off (0x8n) メッセージを処理
    if ((message.status & 0xF0) == 0x90 && message.data2 > 0) { // Note On
        double frequency = MidiSystem::midiNoteToFrequency(message.data1);
        activeNotes[message.data1] = frequency;
        updateSoundDataTexture();
    } else if ((message.status & 0xF0) == 0x80 ||
               ((message.status & 0xF0) == 0x90 && message.data2 == 0)) { // Note Off
        activeNotes.erase(message.data1);
        updateSoundDataTexture();
    }
}

void Application::onAudioCallback(float* buffer, int frames) {
    // バッファを最初にゼロクリア
    std::fill(buffer, buffer + frames, 0.0f);

    if (!isAudioPlaying) {
        return;
    }

    // GPUで生成されたオーディオデータを使用
    {
        std::lock_guard<std::mutex> lock(audioBufferMutex);
        for (int i = 0; i < frames && i < static_cast<int>(gpuAudioBuffer.size()); i++) {
            buffer[i] = gpuAudioBuffer[i];
        }
    }

    totalSamplesProcessed += frames;
}

void Application::render() {
    glClearColor(0.2f, 0.2f, 0.2f, 1.0f);
    glClear(GL_COLOR_BUFFER_BIT);

    renderWaveform();
    renderUI();
}

void Application::renderWaveform() {
    // 波形表示エリアのサイズと位置を計算
    int waveformWidth = windowWidth / 2;
    int waveformHeight = 200;
    int waveformX = 10;
    int waveformY = windowHeight - waveformHeight - 10;

    glViewport(waveformX, waveformY, waveformWidth, waveformHeight);

    if (debugShader && waveTexture && quadRenderer) {
        debugShader->use();
        waveTexture->bind(0);
        debugShader->setInt("u_texture", 0);
        quadRenderer->render();
    }

    // ビューポートをリセット
    glViewport(0, 0, windowWidth, windowHeight);
}

void Application::renderUI() {
    ImGui_ImplOpenGL3_NewFrame();
    ImGui_ImplGlfw_NewFrame();
    ImGui::NewFrame();

    // メインコントロールパネル
    ImGui::Begin("シェーダーで音を鳴らすデモ");

    ImGui::Text("GPUシェーダーで直接オーディオ波形を生成し、ALSA で再生します。");
    ImGui::Separator();

    // オーディオコントロール
    if (ImGui::Button(isAudioPlaying ? "Stop Audio" : "Start Audio")) {
        toggleAudio();
    }

    ImGui::SameLine();
    ImGui::Text("Sample Rate: %d Hz", SAMPLE_RATE);
    ImGui::Text("Buffer Size: %d samples", AUDIO_BUFFER_SIZE);

    ImGui::Separator();

    // MIDIコントロール
    ImGui::Text("MIDI Status:");
    if (midiPorts.empty()) {
        ImGui::Text("MIDI: 入力デバイスが見つかりません");
    } else {
        ImGui::Text("MIDI: %zu個のポートが利用可能", midiPorts.size());

        if (ImGui::BeginCombo("MIDI Port", selectedMidiPort.empty() ? "Select..." : selectedMidiPort.c_str())) {
            for (const auto& port : midiPorts) {
                bool isSelected = (selectedMidiPort == port);
                if (ImGui::Selectable(port.c_str(), isSelected)) {
                    connectMidiPort(port);
                }
                if (isSelected) {
                    ImGui::SetItemDefaultFocus();
                }
            }
            ImGui::EndCombo();
        }
    }

    ImGui::Separator();

    // アクティブノート表示
    ImGui::Text("Active Notes (%zu):", activeNotes.size());
    for (const auto& note : activeNotes) {
        ImGui::Text("Note %d: %.2f Hz", note.first, note.second);
    }

    ImGui::Separator();

    // テスト用ボタン
    ImGui::Text("Test Audio (without MIDI):");
    if (ImGui::Button("Play C4 (261.63 Hz)")) {
        activeNotes[60] = 261.63; // C4
        updateSoundDataTexture();
        std::cout << "Added C4 note, active notes: " << activeNotes.size() << std::endl;
    }
    ImGui::SameLine();
    if (ImGui::Button("Play E4 (329.63 Hz)")) {
        activeNotes[64] = 329.63; // E4
        updateSoundDataTexture();
    }
    ImGui::SameLine();
    if (ImGui::Button("Play G4 (392.00 Hz)")) {
        activeNotes[67] = 392.00; // G4
        updateSoundDataTexture();
    }
    if (ImGui::Button("Stop All")) {
        activeNotes.clear();
        updateSoundDataTexture();
    }

    ImGui::End();

    // 波形表示ウィンドウ
    ImGui::Begin("Waveform Display");
    ImGui::Text("シェーダーが生成した波形テクスチャ");
    ImGui::Text("(OpenGLビューポートに直接描画)");
    ImGui::End();

    ImGui::Render();
    ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());
}

void Application::updateSoundDataTexture() {
    if (!soundDataTexture) {
        std::cout << "updateSoundDataTexture: soundDataTexture is null" << std::endl;
        return;
    }

    std::vector<double> frequencies;
    for (const auto& note : activeNotes) {
        frequencies.push_back(note.second);
    }

    int numNotes = std::max(1, static_cast<int>(frequencies.size()));
    std::vector<float> soundData(numNotes * 2);

    for (int i = 0; i < static_cast<int>(frequencies.size()); i++) {
        soundData[i * 2 + 0] = static_cast<float>(frequencies[i]); // 周波数
        soundData[i * 2 + 1] = 1.0f; // 振幅
    }

    std::cout << "Updating sound data texture: " << frequencies.size()
              << " frequencies, numNotes=" << numNotes << std::endl;
    for (size_t i = 0; i < frequencies.size(); i++) {
        std::cout << "  Freq[" << i << "] = " << frequencies[i] << " Hz" << std::endl;
    }

    soundDataTexture->updateData(soundData.data(), numNotes, 1, GL_RG, GL_FLOAT);
}

void Application::toggleAudio() {
    if (isAudioPlaying) {
        std::cout << "Stopping audio..." << std::endl;
        audioSystem->stop();
        isAudioPlaying = false;
    } else {
        std::cout << "Starting audio..." << std::endl;
        if (audioSystem->start()) {
            isAudioPlaying = true;
            totalSamplesProcessed = 0.0;
            std::cout << "Audio started successfully" << std::endl;
        } else {
            std::cout << "Failed to start audio" << std::endl;
        }
    }
}

void Application::connectMidiPort(const std::string& portName) {
    if (midiSystem->connectToPort(portName)) {
        selectedMidiPort = portName;
    }
}

void Application::generateGPUAudio() {
    if (!audioShader || !audioFramebuffer || !quadRenderer || !isAudioPlaying) {
        return;
    }

    // アクティブノートがない場合は無音
    if (activeNotes.empty()) {
        std::lock_guard<std::mutex> lock(audioBufferMutex);
        std::fill(gpuAudioBuffer.begin(), gpuAudioBuffer.end(), 0.0f);
        return;
    }

    // 現在のOpenGLコンテキストを保存
    GLint currentFBO;
    glGetIntegerv(GL_FRAMEBUFFER_BINDING, &currentFBO);
    GLint viewport[4];
    glGetIntegerv(GL_VIEWPORT, viewport);

    // GPUで波形を生成
    audioFramebuffer->bind();
    glViewport(0, 0, AUDIO_BUFFER_SIZE, 1);

    audioShader->use();

    // Uniformを設定
    double timeOffset = totalSamplesProcessed / SAMPLE_RATE;
    audioShader->setFloat("u_time_offset", static_cast<float>(timeOffset));
    audioShader->setFloat("u_sample_rate", static_cast<float>(SAMPLE_RATE));

    // サウンドデータテクスチャをバインド
    soundDataTexture->bind(0);
    audioShader->setInt("u_sound_data", 0);

    // 描画
    glClear(GL_COLOR_BUFFER_BIT);
    quadRenderer->render();

    // GPUからピクセルデータを読み出し
    std::vector<float> pixelData(AUDIO_BUFFER_SIZE * 4);
    audioFramebuffer->readPixels(0, 0, AUDIO_BUFFER_SIZE, 1, GL_RGBA, GL_FLOAT, pixelData.data());

    // オーディオバッファにコピー
    {
        std::lock_guard<std::mutex> lock(audioBufferMutex);
        const float volume = 0.3f;
        float maxSample = 0.0f;
        for (int i = 0; i < AUDIO_BUFFER_SIZE; i++) {
            float sample = (pixelData[i * 4] - 0.5f) * 2.0f; // -1.0 to 1.0 に変換
            gpuAudioBuffer[i] = sample * volume; // 音量調整
            waveformData[i] = gpuAudioBuffer[i]; // 波形表示用にコピー
            maxSample = std::max(maxSample, std::abs(gpuAudioBuffer[i]));
        }

        // デバッグ出力（最初の数回のみ）
        static int debugCount = 0;
        if (debugCount++ < 5) {
            std::cout << "GPU audio generation: max amplitude: " << maxSample
                      << ", first 8 pixels: ";
            for (int i = 0; i < 8; i++) {
                std::cout << pixelData[i * 4] << " ";
            }
            std::cout << std::endl;
        }
    }

    // OpenGLコンテキストを復元
    glBindFramebuffer(GL_FRAMEBUFFER, currentFBO);
    glViewport(viewport[0], viewport[1], viewport[2], viewport[3]);
}

void Application::framebufferSizeCallback(GLFWwindow* window, int width, int height) {
    Application* app = static_cast<Application*>(glfwGetWindowUserPointer(window));
    if (app) {
        app->windowWidth = width;
        app->windowHeight = height;
        glViewport(0, 0, width, height);
    }
}

bool Application::initializeImGui() {
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO();
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;
    
    ImGui::StyleColorsDark();
    
    if (!ImGui_ImplGlfw_InitForOpenGL(window, true)) {
        std::cerr << "Failed to initialize ImGui GLFW backend" << std::endl;
        return false;
    }
    
    if (!ImGui_ImplOpenGL3_Init("#version 330")) {
        std::cerr << "Failed to initialize ImGui OpenGL backend" << std::endl;
        return false;
    }
    
    return true;
}
